import csv

def fix_csv_formatting(input_file, output_file):
    """
    Fix the CSV formatting to maintain consistency with the original format.
    This version writes the file manually to avoid automatic quoting.
    """
    with open(input_file, 'r', newline='') as infile:
        reader = csv.reader(infile)
        
        with open(output_file, 'w', newline='') as outfile:
            for i, row in enumerate(reader):
                if i == 0:  # Header row - write as-is
                    outfile.write(','.join(row) + '\n')
                    continue
                    
                # Check if the last column doesn't contain 'Yes' at the end
                last_col = row[-1].strip()
                if not last_col.endswith('Yes'):
                    # Add ', No' to the last column
                    row[-1] = row[-1] + ',No'
                
                # Write the row manually to maintain original formatting
                outfile.write(','.join(row) + '\n')
    
    print(f"Processing complete! Fixed CSV saved as '{output_file}'")

# Process the original file
fix_csv_formatting('101Orders.csv', '101Orders_fixed.csv')

# Verification
print("\nVerification:")
with open('101Orders_fixed.csv', 'r') as file:
    reader = csv.reader(file)
    yes_count = 0
    no_count = 0
    
    for i, row in enumerate(reader):
        if i == 0:  # Skip header
            continue
        last_col = row[-1].strip()
        if last_col.endswith('Yes'):
            yes_count += 1
        elif last_col.endswith('No'):
            no_count += 1
    
    print(f"Rows ending with 'Yes': {yes_count}")
    print(f"Rows ending with 'No': {no_count}")
    print(f"Total data rows: {yes_count + no_count}")

print("\nSample of results:")
with open('101Orders_fixed.csv', 'r') as file:
    lines = file.readlines()
    print("Header:", lines[0].strip())
    print("First data row:", lines[1].strip())
    print("Row with Yes (line 12):", lines[12].strip())
