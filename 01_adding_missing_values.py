import csv

# Read the CSV file and process the last column
def add_missing_values(input_file, output_file):
    with open(input_file, 'r', newline='') as infile:
        reader = csv.reader(infile)
        rows = []

        for i, row in enumerate(reader):
            if i == 0:  # Header row - don't modify
                rows.append(row)
                continue

            # Check if the last column doesn't contain 'Yes' at the end
            last_col = row[-1].strip()  # Remove any trailing whitespace
            if not last_col.endswith('Yes'):
                # Add ', No' to the last column
                row[-1] = row[-1] + ', No'
            # If it already ends with 'Yes', leave it unchanged
            rows.append(row)

    # Write the modified data to a new CSV file
    with open(output_file, 'w', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerows(rows)

    print(f"Processing complete! Modified CSV saved as '{output_file}'")

# Process the file
add_missing_values('101Orders.csv', '101Orders_modified.csv')

# Verify the results
print("\nVerification:")
with open('101Orders_modified.csv', 'r') as file:
    reader = csv.reader(file)
    yes_count = 0
    no_count = 0

    for i, row in enumerate(reader):
        if i == 0:  # Skip header
            continue
        last_col = row[-1].strip()
        if last_col.endswith('Yes'):
            yes_count += 1
        elif last_col.endswith('No'):
            no_count += 1

    print(f"Rows ending with 'Yes': {yes_count}")
    print(f"Rows ending with 'No': {no_count}")
    print(f"Total data rows: {yes_count + no_count}")

print("\nSample of modified entries:")
with open('101Orders_modified.csv', 'r') as file:
    reader = csv.reader(file)
    for i, row in enumerate(reader):
        if i == 0:
            print(f"Header: Last column = '{row[-1]}'")
        elif i <= 3:
            print(f"Row {i}: Last column = '{row[-1]}'")
        elif i == 12:  # This should be a 'Yes' row
            print(f"Row {i} (Yes example): Last column = '{row[-1]}'")
            break
