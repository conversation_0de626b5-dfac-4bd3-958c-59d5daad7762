import csv

print("=== CSV Processing Results ===\n")

# Count the results
with open('101Orders_modified.csv', 'r') as file:
    reader = csv.reader(file)
    yes_count = 0
    no_count = 0
    total_rows = 0
    
    for i, row in enumerate(reader):
        if i == 0:  # Skip header
            continue
        total_rows += 1
        last_col = row[-1].strip()
        if last_col.endswith('Yes'):
            yes_count += 1
        elif last_col.endswith('No'):
            no_count += 1

print(f"Total data rows processed: {total_rows}")
print(f"Rows with ', Yes': {yes_count}")
print(f"Rows with ', No': {no_count}")
print(f"Success: {yes_count + no_count == total_rows}")

print("\n=== Sample Results ===")
print("Rows that originally had ', Yes' (preserved):")
with open('101Orders_modified.csv', 'r') as file:
    reader = csv.reader(file)
    yes_examples = 0
    for i, row in enumerate(reader):
        if i == 0:
            continue
        if row[-1].strip().endswith('Yes') and yes_examples < 3:
            print(f"  Row {i}: {row[0]} -> Last column: '{row[-1]}'")
            yes_examples += 1

print("\nRows that got ', No' added:")
with open('101Orders_modified.csv', 'r') as file:
    reader = csv.reader(file)
    no_examples = 0
    for i, row in enumerate(reader):
        if i == 0:
            continue
        if row[-1].strip().endswith('No') and no_examples < 3:
            print(f"  Row {i}: {row[0]} -> Last column: '{row[-1]}'")
            no_examples += 1
